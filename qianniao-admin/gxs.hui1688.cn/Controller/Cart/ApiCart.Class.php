<?php
/**
 * 购物车
 * Created by PhpStorm.
 */
namespace Jin<PERSON>ouYun\Controller\Cart;

use Mall\Framework\Core\ErrorCode;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\Controller\BaseController;

use <PERSON><PERSON><PERSON>Y<PERSON>\Model\Cart\MCart;
use Mall\Framework\Core\StatusCode;

class ApiCart extends BaseController
{
    private $objMCart;

    /**
     * ApiCart constructor.
     * @param bool $isCheckAcl
     * @param bool $isMustLogin
     * @param bool $checkToken
     * @param bool $getAreaCode
     * @throws \Exception
     */
    public function __construct($isCheckAcl = false, $isMustLogin = true ,$checkToken=true,$getAreaCode=true)
    {
        parent::__construct($isCheckAcl, $isMustLogin, $checkToken = true, $getAreaCode = true);
        $this->objMCart = new MCart($this->onlineUserId, $this->onlineEnterpriseId,true,$this->areaCode);
    }

    /**
     * 添加购物车,公共字段
     *
     * @return array
     */
    public function commonFieldFilter()
    {
        $params = $this->request->getRawJson();
        if (empty($params)) {
            parent::sendOutput('参数为空', ErrorCode::$paramError);
        }

        $cartData = [
            'goodsData' => $params['goodsData'] ?? [], //商品数据
        ];

        foreach ($cartData as $key => $value) {
            if (empty($value) && $value !== 0) {
                parent::sendOutput($key . '参数错误', ErrorCode::$paramError);
            }
        }

        $goodsData = [];
        foreach ($cartData['goodsData'] as $key => $val) {
            $goodsData[$key] = [
                'goodsBasicId' => $val['goodsBasicId'] ?? '',
                'goodsId'      => $val['goodsId'] ?? '',
                'skuId'        => $val['skuId'] ?? '',
                'buyNum'       => $val['buyNum'] ?? '',
                'shopId'       => $val['shopId'] ?? '',
                'source'       => $val['source'] ?? '',
                'sourceType'   => $val['sourceType'] ?? 1,
            ];

            foreach ($goodsData[$key] as $k => $v) {
                if (empty($v)) {
                    parent::sendOutput($k . '参数错误', ErrorCode::$paramError);
                }
            }
            $goodsData[$key]['fullGiveId'] = $val['fullGiveId'] ?? null; // 满赠活动ID
            $goodsData[$key]['goodsCode'] = $this->objUtil->createCode(StatusCode::$code['goodsBasic']['prefix'], $goodsData[$key]['goodsId'], StatusCode::$code['goodsBasic']['length']);
            $goodsData[$key]['warehouseId'] = $val['warehouseId'] ?? null;
        }
        $cartData['goodsData'] = $goodsData;//过滤后数据
        return $cartData;
    }

    /**
     * 加入购物车
     * @throws \Exception
     */
    public function addCart()
    {
        $cartData = $this->commonFieldFilter();
        $result = $this->objMCart->addCartApi($cartData);
        if ($result->isSuccess()) {
            parent::sendOutput($result->getData());
        }
        parent::sendOutput($result->getData(), $result->getErrorCode());
    }

    /**
     * 更新商品数量(购物车内操作)
     * @throws \Exception
     */
    public function updateBuyNum()
    {
        $params['cartId'] = $this->request->param('request_id');
        $params['buyNum'] = $this->request->param('buyNum');

        foreach ($params as $key => $value) {
            if (empty($value) && $value !== 0) {
                $this->sendOutput($key . '参数错误', ErrorCode::$paramError);
            }
        }
        $result = $this->objMCart->updateBuyNumApi($params);
        if ($result->isSuccess()) {
            parent::sendOutput($result->getData());
        }
        parent::sendOutput($result->getData(), $result->getErrorCode());
    }

    /**
     * 获取用户购物车数据
     * @throws \Exception
     */
    public function getCartByUserCenterId()
    {
        $result = $this->objMCart->getCartByUserCenterIdApi();
        if ($result->isSuccess()) {
            $returnData = $result->getData();
            $pageData = [
                'pageIndex' => 0,
                'pageSize'  => 0,
                'pageTotal' => $returnData['total']
            ];
            parent::sendOutput($returnData['data'], 0, $pageData);
        }
        parent::sendOutput($result->getData(), ErrorCode::$dberror);
    }

    /**
     * 更新选中状态
     * @throws \Exception
     */
    public function updateSelection()
    {
        $paramsData = $this->request->getRawJson();
        $params = [
            'selection' => isset($paramsData['selection']) ? $paramsData['selection'] : '',//4=>未选中 5=>已选中
            'type'      => isset($paramsData['type']) ? $paramsData['type'] : 0,//type 1单个 2店铺 3全选
        ];

        foreach ($params as $key => $value) {
            if (empty($value)) {
                $this->sendOutput($key . '参数错误', ErrorCode::$paramError);
            }
        }
        $params['id'] = isset($paramsData['cartId']) ? $paramsData['cartId'] : 0;//cartId
        $params['shopId'] = isset($paramsData['shopId']) ? $paramsData['shopId'] : 0;//店铺id

        $result = $this->objMCart->updateSelection($params);
        if ($result->isSuccess()) {
            parent::sendOutput($result->getData());
        }
        parent::sendOutput($result->getData(), $result->getErrorCode());
    }

    /**
     * 删除购物车中商品(可批量)
     */
    public function delCart()
    {
        $params = $this->request->getRawJson();
        $paramsData = [
            'id' => isset($params['cartId']) ? $params['cartId'] : 0,
        ];

        foreach ($paramsData as $key => $value) {
            if (empty($value) && $value !== 0) {
                $this->sendOutput($key . '参数错误', ErrorCode::$paramError);
            }
        }
        $result = $this->objMCart->delCart($paramsData);
        if ($result->isSuccess()) {
            parent::sendOutput($result->getData());
        }
        parent::sendOutput($result->getData(), $result->getErrorCode());
    }

    /**
     * 确认订单接口
     */
    public function confirmationOfOrder()
    {
        $vipCardId = $this->request->param('vipCardId');//会员卡id
        $userCouponId=$this->request->param('userCouponId');//优惠券id
        $addressId = $this->request->param('addressId');//收货地址id
        $deliveryId = $this->request->param('deliveryId');//配送方式id
        $proporties = $this->request->param('proporties');//店铺对应对应的收货地址以及配送方式
        $result = $this->objMCart->confirmationOfOrder($userCouponId,$vipCardId,$addressId,$deliveryId);
        if ($result->isSuccess()) {
            parent::sendOutput($result->getData());
        }
        parent::sendOutput($result->getData(), $result->getErrorCode());
    }

    /**
     * 商品列表用
     * @throws \Exception
     */
    public function updateCartNum()
    {
        $paramsData = $this->request->getRawJson();
        if (empty($paramsData)) {
            $this->sendOutput('参数为空', ErrorCode::$paramError);
        }
        $params = [
            'goodsId' => $this->request->param('request_id'),
            'buyNum'  => isset($paramsData['buyNum']) ? $paramsData['buyNum'] : '',
            'skuId'   => isset($paramsData['skuId']) ? $paramsData['skuId'] : '',
        ];
        foreach ($params as $key => $value) {
            if (empty($value) && $value !== 0) {
                $this->sendOutput($key . '参数错误', ErrorCode::$paramError);
            }
        }
        $result = $this->objMCart->updateCartNum($params);
        if ($result->isSuccess()) {
            parent::sendOutput($result->getData());
        }
        parent::sendOutput($result->getData(), $result->getErrorCode());
    }

    /**
     * 获取当前用户会员卡
     */
    public function getVipCard()
    {
        $result = $this->objMCart->getVipCard([]);
        if ($result->isSuccess()) {
            parent::sendOutput($result->getData());
        }
        parent::sendOutput($result->getData(), $result->getErrorCode());
    }

    /**
     * Doc: (des="立即购买")
     * User: XMing
     * Date: 2021/1/22
     * Time: 4:01 下午
     * @throws \Exception
     */
    public function buyNow()
    {
        $params = $this->request->getRawJson();
        if (empty($params)) {
            parent::sendOutput('参数为空', ErrorCode::$paramError);
        }
        if (!isset($params['lists']) || empty($params['lists'])){
            parent::sendOutput('请选择商品',ErrorCode::$paramError);
        }
        $lists = getArrayItem($params,'lists',[]);


        $data = [
            'sourceType' => getArrayItem($params,'sourceType',0),
            'comBinId'   => getArrayItem($params,'couponId',0),
            'couponId'   => getArrayItem($params,'couponId',0),
            'addressId'  => getArrayItem($params,'addressId',0),
            'deliveryId' => getArrayItem($params,'deliveryId',0),
        ];
        $result = $this->objMCart->buyNow($lists,$data);
        if ($result->isSuccess()) {
            parent::sendOutput($result->getData());
        }
        parent::sendOutput($result->getData(), $result->getErrorCode());
    }

    /**
     * 普通商品立即购买
     */
    public function goodsBuyNow()
    {
        $params = $this->request->getRawJson();
        if (empty($params)) {
            parent::sendOutput('参数为空', ErrorCode::$paramError);
        }
        $goods = [
            'goodsId' => $params['goodsId'] ?? '',
            'goodsBasicId' => getArrayItem($params, 'goodsBasicId', 0),
            'skuId'   => $params['skuId'] ?? '',
            'buyNum'  => $params['buyNum'] ?? '',
            'shopId'  => getArrayItem($params, 'shopId', 0),
            'selection' => StatusCode::$standard,
        ];
        foreach ($goods as $key => $val) {
            if (empty($val)) {
                parent::sendOutput($key . '参数错误', ErrorCode::$paramError);
            }
        }
        $goods['activityId'] = getArrayItem($params, 'activityId', 0);
        $goods['warehouseId'] = getArrayItem($params, 'warehouseId', 0);

        $data['vipCardId'] = getArrayItem($params, 'vipCardId', 0);
        $data['couponId'] = $params['couponId'] ?? 0;
        $data['addressId'] = $params['addressId'] ?? 0;
        $data['deliveryId'] = $params['deliveryId'] ?? 0;

        $result = $this->objMCart->goodsBuyNow($goods,$data);
        if ($result->isSuccess()) {
            parent::sendOutput($result->getData());
        }
        parent::sendOutput($result->getData(), $result->getErrorCode());
    }

    /**
     * 验证满赠活动条件
     * @throws \Exception
     */
    public function validateFullGiveCondition()
    {
        $fullGiveId = $this->request->param('fullGiveId');

        if (empty($fullGiveId)) {
            $this->sendOutput('满赠活动ID不能为空', ErrorCode::$paramError);
        }

        $result = $this->objMCart->validateCartFullGiveCondition($fullGiveId);
        if ($result->isSuccess()) {
            parent::sendOutput($result->getData());
        }
        parent::sendOutput($result->getData(), $result->getErrorCode());
    }

    /**
     * 将满赠赠品转换为正常商品
     * @throws \Exception
     */
    public function convertGiftToNormalGoods()
    {
        $cartId = $this->request->param('cartId');

        if (empty($cartId)) {
            $this->sendOutput('购物车商品ID不能为空', ErrorCode::$paramError);
        }

        $result = $this->objMCart->convertGiftToNormalGoods($cartId);
        if ($result->isSuccess()) {
            parent::sendOutput($result->getData());
        }
        parent::sendOutput($result->getData(), $result->getErrorCode());
    }

    /**
     * 验证购物车中所有满赠活动条件
     * @throws \Exception
     */
    public function validateCartFullGiveConditions()
    {
        $action = $this->request->param('action', 'check'); // check, remove, convert

        $result = $this->objMCart->validateCartFullGiveConditions($action);
        if ($result->isSuccess()) {
            parent::sendOutput($result->getData());
        }
        parent::sendOutput($result->getData(), $result->getErrorCode());
    }

    /**
     * 批量处理不满足条件的满赠赠品
     * @throws \Exception
     */
    public function handleInvalidGifts()
    {
        $invalidGifts = $this->request->param('invalidGifts');
        $action = $this->request->param('action', 'remove'); // remove, convert

        if (empty($invalidGifts) || !is_array($invalidGifts)) {
            $this->sendOutput('无效的赠品数据', ErrorCode::$paramError);
        }

        $result = $this->objMCart->handleInvalidGifts($invalidGifts, $action);
        if ($result->isSuccess()) {
            parent::sendOutput($result->getData());
        }
        parent::sendOutput($result->getData(), $result->getErrorCode());
    }

    /**
     * 将正常商品转换为满赠赠品
     * @throws \Exception
     */
    public function convertNormalGoodsToGift()
    {
        $cartId = $this->request->param('cartId');
        $fullGiveId = $this->request->param('fullGiveId');

        if (empty($cartId)) {
            $this->sendOutput('购物车商品ID不能为空', ErrorCode::$paramError);
        }

        if (empty($fullGiveId)) {
            $this->sendOutput('满赠活动ID不能为空', ErrorCode::$paramError);
        }

        $result = $this->objMCart->convertNormalGoodsToGift($cartId, $fullGiveId);
        if ($result->isSuccess()) {
            parent::sendOutput($result->getData());
        }
        parent::sendOutput($result->getData(), $result->getErrorCode());
    }

    /**
     * 批量处理满赠活动赠品转换
     * @throws \Exception
     */
    public function handleGiftConversion()
    {
        $giftItems = $this->request->param('giftItems');
        $fullGiveId = $this->request->param('fullGiveId');

        if (empty($giftItems) || !is_array($giftItems)) {
            $this->sendOutput('赠品数据不能为空', ErrorCode::$paramError);
        }

        if (empty($fullGiveId)) {
            $this->sendOutput('满赠活动ID不能为空', ErrorCode::$paramError);
        }

        $result = $this->objMCart->handleGiftConversion($giftItems, $fullGiveId);
        if ($result->isSuccess()) {
            parent::sendOutput($result->getData());
        }
        parent::sendOutput($result->getData(), $result->getErrorCode());
    }

    /**
     * 测试赠品转换功能
     * @throws \Exception
     */
    public function testGiftConversion()
    {
        $cartId = $this->request->param('cartId');
        $fullGiveId = $this->request->param('fullGiveId', 1); // 默认测试活动ID为1

        if (empty($cartId)) {
            $this->sendOutput('购物车商品ID不能为空', ErrorCode::$paramError);
        }

        // 获取当前商品状态
        $cartResult = $this->objMCart->getCartByUserCenterId();
        if (!$cartResult->isSuccess()) {
            parent::sendOutput('获取购物车数据失败: ' . $cartResult->getData(), $cartResult->getErrorCode());
        }

        $cartData = $cartResult->getData();
        $currentItem = null;

        // 查找指定的购物车商品
        if (!empty($cartData['data']['goodsData'])) {
            foreach ($cartData['data']['goodsData'] as $shop) {
                foreach ($shop['shopGoodsData'] as $goods) {
                    if ($goods['cartId'] == $cartId) {
                        $currentItem = $goods;
                        break 2;
                    }
                }
            }
        }

        if (!$currentItem) {
            parent::sendOutput('未找到指定的购物车商品', ErrorCode::$paramError);
        }

        // 执行转换测试
        $result = $this->objMCart->convertNormalGoodsToGift($cartId, $fullGiveId);

        $response = [
            'testResult' => $result->isSuccess(),
            'message' => $result->isSuccess() ? '转换成功' : $result->getData(),
            'beforeConversion' => [
                'cartId' => $currentItem['cartId'],
                'sourceType' => $currentItem['sourceType'],
                'extends' => $currentItem['extends'] ?? null
            ],
            'conversionData' => $result->getData()
        ];

        parent::sendOutput($response);
    }

    /**
     * 动态处理购物车满赠活动商品状态转换
     * 根据满赠条件自动转换商品状态：满足条件时转为赠品，不满足时转为普通商品
     * @throws \Exception
     */
    public function handleFullGiveStatusConversion()
    {
        $params = $this->request->getRawJson();
        $action = $params['action'] ?? 'auto'; // auto: 自动处理, check: 仅检查, force: 强制转换
        $fullGiveId = $params['fullGiveId'] ?? null; // 指定满赠活动ID，为空则处理所有
        $cartIds = $params['cartIds'] ?? []; // 指定购物车商品ID列表，为空则处理所有

        try {
            $result = $this->objMCart->handleFullGiveStatusConversion($action, $fullGiveId, $cartIds);
            if ($result->isSuccess()) {
                parent::sendOutput($result->getData());
            }
            parent::sendOutput($result->getData(), $result->getErrorCode());
        } catch (\Exception $e) {
            parent::sendOutput('处理满赠状态转换失败: ' . $e->getMessage(), ErrorCode::$paramError);
        }
    }

    /**
     * 实时检查购物车满赠条件并返回状态变化建议
     * @throws \Exception
     */
    public function checkFullGiveStatusChanges()
    {
        $params = $this->request->getRawJson();
        $includeDetails = $params['includeDetails'] ?? false; // 是否包含详细信息

        try {
            $result = $this->objMCart->checkFullGiveStatusChanges($includeDetails);
            if ($result->isSuccess()) {
                parent::sendOutput($result->getData());
            }
            parent::sendOutput($result->getData(), $result->getErrorCode());
        } catch (\Exception $e) {
            parent::sendOutput('检查满赠状态变化失败: ' . $e->getMessage(), ErrorCode::$paramError);
        }
    }

    /**
     * 批量转换满赠商品状态
     * @throws \Exception
     */
    public function batchConvertFullGiveStatus()
    {
        $params = $this->request->getRawJson();

        if (empty($params['conversions']) || !is_array($params['conversions'])) {
            $this->sendOutput('转换数据不能为空', ErrorCode::$paramError);
        }

        $conversions = $params['conversions']; // 格式: [{'cartId': 1, 'action': 'toGift', 'fullGiveId': 1}, ...]
        $validateOnly = $params['validateOnly'] ?? false; // 仅验证不执行

        try {
            $result = $this->objMCart->batchConvertFullGiveStatus($conversions, $validateOnly);
            if ($result->isSuccess()) {
                parent::sendOutput($result->getData());
            }
            parent::sendOutput($result->getData(), $result->getErrorCode());
        } catch (\Exception $e) {
            parent::sendOutput('批量转换满赠状态失败: ' . $e->getMessage(), ErrorCode::$paramError);
        }
    }

    /**
     * 获取购物车满赠状态概览（用于调试和监控）
     * @throws \Exception
     */
    public function getFullGiveStatusOverview()
    {
        try {
            // 获取购物车数据
            $cartResult = $this->objMCart->getCartByUserCenterIdApi();
            if (!$cartResult->isSuccess()) {
                parent::sendOutput('获取购物车数据失败', $cartResult->getErrorCode());
            }

            $cartData = $cartResult->getData();
            $overview = [
                'totalItems' => 0,
                'normalGoods' => 0,
                'giftGoods' => 0,
                'fullGiveActivities' => [],
                'statusSummary' => []
            ];

            if (!empty($cartData['data']['goodsData'])) {
                foreach ($cartData['data']['goodsData'] as $shop) {
                    foreach ($shop['shopGoodsData'] as $goods) {
                        $overview['totalItems']++;

                        if ($goods['sourceType'] == 2) {
                            // 赠品
                            $overview['giftGoods']++;
                            $extends = json_decode($goods['extends'], true);
                            $fullGiveId = $extends['fullGiveId'] ?? null;

                            if ($fullGiveId) {
                                if (!isset($overview['fullGiveActivities'][$fullGiveId])) {
                                    $overview['fullGiveActivities'][$fullGiveId] = [
                                        'id' => $fullGiveId,
                                        'giftCount' => 0,
                                        'gifts' => []
                                    ];
                                }
                                $overview['fullGiveActivities'][$fullGiveId]['giftCount']++;
                                $overview['fullGiveActivities'][$fullGiveId]['gifts'][] = [
                                    'cartId' => $goods['cartId'],
                                    'goodsName' => $goods['goodsName'],
                                    'skuId' => $goods['skuId']
                                ];
                            }
                        } else {
                            // 普通商品
                            $overview['normalGoods']++;
                        }
                    }
                }
            }

            // 检查状态变化建议
            $changesResult = $this->objMCart->checkFullGiveStatusChanges(true);
            if ($changesResult->isSuccess()) {
                $changesData = $changesResult->getData();
                $overview['statusSummary'] = [
                    'hasChanges' => $changesData['hasChanges'],
                    'totalSuggestions' => $changesData['totalSuggestions'],
                    'suggestions' => $changesData['suggestions']
                ];
            }

            parent::sendOutput($overview);
        } catch (\Exception $e) {
            parent::sendOutput('获取满赠状态概览失败: ' . $e->getMessage(), ErrorCode::$paramError);
        }
    }

}

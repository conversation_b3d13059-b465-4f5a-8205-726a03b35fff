# 满赠活动商品状态转换功能测试

## 测试环境准备

### 1. 数据准备
- 创建一个满赠活动（例如：满100元赠商品A）
- 准备测试商品（包括满赠的目标商品和赠品）
- 准备测试用户账号

### 2. 测试场景

## 场景1：添加商品触发满赠条件

### 测试步骤
1. 清空购物车
2. 添加价值50元的商品A到购物车
3. 检查购物车状态（应该没有赠品）
4. 再添加价值60元的商品B到购物车（总计110元，满足满100元条件）
5. 检查购物车状态（应该自动添加赠品或转换现有商品为赠品）

### 预期结果
- 购物车总金额达到满赠条件后，系统自动处理赠品状态
- 响应中包含 `giftChanges` 信息，说明状态变化

### 测试接口
```bash
# 1. 添加第一个商品
curl -X POST "http://your-domain/Cart/ApiCart/addCart" \
  -H "Content-Type: application/json" \
  -d '{
    "goodsData": [{
      "goodsId": 1,
      "skuId": 1,
      "buyNum": 1,
      "shopId": 1,
      "source": "normal",
      "sourceType": 1,
      "goodsBasicId": 1
    }]
  }'

# 2. 添加第二个商品
curl -X POST "http://your-domain/Cart/ApiCart/addCart" \
  -H "Content-Type: application/json" \
  -d '{
    "goodsData": [{
      "goodsId": 2,
      "skuId": 2,
      "buyNum": 1,
      "shopId": 1,
      "source": "normal",
      "sourceType": 1,
      "goodsBasicId": 2
    }]
  }'

# 3. 检查购物车状态
curl -X GET "http://your-domain/Cart/ApiCart/getCartByUserCenterId"

# 4. 获取满赠状态概览
curl -X GET "http://your-domain/Cart/ApiCart/getFullGiveStatusOverview"
```

## 场景2：减少商品数量不满足满赠条件

### 测试步骤
1. 在满足满赠条件的购物车基础上
2. 减少商品数量，使总金额低于满赠门槛
3. 检查赠品是否自动转换为普通商品

### 测试接口
```bash
# 1. 减少商品数量
curl -X POST "http://your-domain/Cart/ApiCart/updateBuyNum" \
  -H "Content-Type: application/json" \
  -d '{
    "cartId": 1,
    "buyNum": 0.5
  }'

# 2. 检查状态变化
curl -X POST "http://your-domain/Cart/ApiCart/checkFullGiveStatusChanges" \
  -H "Content-Type: application/json" \
  -d '{
    "includeDetails": true
  }'
```

## 场景3：手动状态转换

### 测试步骤
1. 手动将普通商品转换为赠品
2. 手动将赠品转换为普通商品
3. 批量转换多个商品状态

### 测试接口
```bash
# 1. 单个商品转换为赠品
curl -X POST "http://your-domain/Cart/ApiCart/convertNormalGoodsToGift" \
  -H "Content-Type: application/json" \
  -d '{
    "cartId": 1,
    "fullGiveId": 1
  }'

# 2. 赠品转换为普通商品
curl -X POST "http://your-domain/Cart/ApiCart/convertGiftToNormalGoods" \
  -H "Content-Type: application/json" \
  -d '{
    "cartId": 1
  }'

# 3. 批量转换
curl -X POST "http://your-domain/Cart/ApiCart/batchConvertFullGiveStatus" \
  -H "Content-Type: application/json" \
  -d '{
    "conversions": [
      {
        "cartId": 1,
        "action": "toGift",
        "fullGiveId": 1
      },
      {
        "cartId": 2,
        "action": "toNormal"
      }
    ],
    "validateOnly": false
  }'
```

## 场景4：自动状态处理

### 测试步骤
1. 调用自动状态处理接口
2. 检查处理结果

### 测试接口
```bash
# 自动处理所有满赠状态
curl -X POST "http://your-domain/Cart/ApiCart/handleFullGiveStatusConversion" \
  -H "Content-Type: application/json" \
  -d '{
    "action": "auto"
  }'

# 仅检查不执行
curl -X POST "http://your-domain/Cart/ApiCart/handleFullGiveStatusConversion" \
  -H "Content-Type: application/json" \
  -d '{
    "action": "check"
  }'
```

## 验证要点

### 1. 数据库状态验证
检查购物车表中的字段变化：
```sql
-- 查看购物车商品状态
SELECT id, goodsId, skuId, sourceType, extends, buyNum 
FROM qianniao_cart_{enterpriseId} 
WHERE userCenterId = {userId};
```

### 2. 响应数据验证
- 检查接口响应中的 `giftChanges` 字段
- 验证转换结果的准确性
- 确认错误处理的正确性

### 3. 日志验证
检查系统日志中的相关记录：
- 满赠条件检查日志
- 状态转换操作日志
- 错误处理日志

## 性能测试

### 大量商品测试
1. 创建包含100+商品的购物车
2. 测试自动状态处理的性能
3. 验证批量操作的效率

### 并发测试
1. 模拟多用户同时操作购物车
2. 测试状态转换的并发安全性
3. 验证数据一致性

## 异常情况测试

### 1. 满赠活动不存在
- 测试引用不存在的满赠活动ID
- 验证错误处理机制

### 2. 商品不存在
- 测试转换不存在的购物车商品
- 验证参数验证机制

### 3. 网络异常
- 模拟网络中断情况
- 测试事务回滚机制

## 测试检查清单

- [ ] 满足满赠条件时自动转换为赠品
- [ ] 不满足满赠条件时自动转换为普通商品
- [ ] 手动转换功能正常
- [ ] 批量转换功能正常
- [ ] 状态检查功能正常
- [ ] 错误处理机制完善
- [ ] 日志记录完整
- [ ] 性能表现良好
- [ ] 数据一致性保证
- [ ] 向后兼容性良好

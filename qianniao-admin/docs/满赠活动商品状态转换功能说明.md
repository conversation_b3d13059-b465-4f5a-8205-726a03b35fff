# 满赠活动商品状态转换功能说明

## 功能概述

本功能实现了购物车中满赠活动商品状态的动态转换逻辑，能够根据满赠条件的满足情况自动在"普通商品"和"赠品"状态之间进行转换。

## 核心场景

### 场景1：满足满赠条件时
- 当购物车中的活动商品总金额或数量达到满赠活动的门槛条件时
- 如果购物车中已存在对应的赠品商品，需要将这些商品的状态从"普通商品"转换为"赠品"
- 赠品应该显示为免费（价格为0），并标识为赠品状态

### 场景2：不满足满赠条件时
- 当购物车中的活动商品总金额或数量未达到满赠活动的门槛条件时
- 如果购物车中存在标记为"赠品"状态的商品，需要将这些商品转换回"普通商品"状态
- 恢复商品的原始价格，移除赠品标识

## 新增接口

### 1. 动态处理满赠状态转换
**接口路径**: `/Cart/ApiCart/handleFullGiveStatusConversion`
**请求方法**: POST
**请求参数**:
```json
{
    "action": "auto",           // 处理动作：auto(自动处理), check(仅检查), force(强制转换)
    "fullGiveId": 1,           // 可选：指定满赠活动ID，为空则处理所有
    "cartIds": [1, 2, 3]       // 可选：指定购物车商品ID列表，为空则处理所有
}
```

### 2. 实时检查满赠状态变化
**接口路径**: `/Cart/ApiCart/checkFullGiveStatusChanges`
**请求方法**: POST
**请求参数**:
```json
{
    "includeDetails": true     // 是否包含详细信息
}
```

### 3. 批量转换满赠状态
**接口路径**: `/Cart/ApiCart/batchConvertFullGiveStatus`
**请求方法**: POST
**请求参数**:
```json
{
    "conversions": [
        {
            "cartId": 1,
            "action": "toGift",    // toGift: 转为赠品, toNormal: 转为普通商品
            "fullGiveId": 1
        }
    ],
    "validateOnly": false      // 仅验证不执行
}
```

### 4. 获取满赠状态概览
**接口路径**: `/Cart/ApiCart/getFullGiveStatusOverview`
**请求方法**: GET
**功能**: 获取当前购物车的满赠状态概览，用于调试和监控

## 自动触发机制

以下操作会自动触发满赠状态检查和转换：

1. **添加商品到购物车** (`addCartApi`)
   - 添加商品后自动检查满赠条件
   - 如有状态变化会在响应中返回变化信息

2. **更新购物车商品数量** (`updateBuyNumApi`)
   - 更新数量后自动检查满赠条件
   - 如有状态变化会在响应中返回变化信息

## 技术实现

### 核心方法

1. **handleFullGiveStatusConversion**: 主要的状态转换处理方法
2. **checkFullGiveStatusChanges**: 检查状态变化建议
3. **batchConvertFullGiveStatus**: 批量状态转换
4. **autoHandleFullGiveStatus**: 自动处理满赠状态（在购物车更新时调用）

### 辅助方法

1. **checkFullGiveConditionForShop**: 检查指定店铺的满赠条件
2. **handleNormalToGiftConversion**: 处理普通商品转为赠品
3. **checkShopFullGiveChanges**: 检查店铺的满赠状态变化建议
4. **validateConversion**: 验证转换操作的有效性
5. **executeConversion**: 执行转换操作

## 数据库字段说明

### 购物车表字段
- `sourceType`: 商品来源类型
  - 1: 普通商品
  - 2: 满赠赠品
  - 3: 满额换购
- `extends`: 扩展信息（JSON格式）
  - 对于满赠赠品，存储 `{"fullGiveId": 活动ID}`

## 使用示例

### 1. 自动处理示例
```javascript
// 添加商品到购物车时，系统会自动处理满赠状态
const response = await fetch('/Cart/ApiCart/addCart', {
    method: 'POST',
    body: JSON.stringify({
        goodsData: [{
            goodsId: 1,
            skuId: 1,
            buyNum: 2,
            // ... 其他参数
        }]
    })
});

// 响应可能包含状态变化信息
if (response.giftChanges) {
    console.log('商品状态发生变化:', response.giftChanges);
}
```

### 2. 手动检查状态变化
```javascript
const response = await fetch('/Cart/ApiCart/checkFullGiveStatusChanges', {
    method: 'POST',
    body: JSON.stringify({
        includeDetails: true
    })
});

if (response.hasChanges) {
    console.log('发现状态变化建议:', response.suggestions);
}
```

## 注意事项

1. **事务处理**: 所有状态转换操作都在数据库事务中进行，确保数据一致性
2. **错误处理**: 转换失败时会记录详细的错误日志
3. **性能考虑**: 大量商品时建议使用批量处理接口
4. **兼容性**: 新功能向后兼容，不影响现有的购物车功能

## 日志记录

系统会记录以下关键操作的日志：
- 满赠条件检查结果
- 商品状态转换操作
- 转换失败的错误信息
- 自动处理的触发和结果

日志可用于问题排查和功能监控。
